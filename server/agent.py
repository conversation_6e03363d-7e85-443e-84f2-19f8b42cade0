import logging
import time
import asyncio
from dotenv import load_dotenv
import json
from datetime import datetime
from PIL import Image
import os

# Load environment variables first
load_dotenv()

from livekit.agents import (
    AgentSession,
    JobContext,
    RoomInputOptions,
    RoomOutputOptions,
    WorkerOptions,
    cli,
)
from livekit.plugins import hedra

from agents import MultiAgentInterviewer
from storage import GCPEgressManager
from helpers import (
    setup_event_handlers,
    cleanup_resources,
    parse_participant_metadata,
    get_recording_filename_prefix,
    log_metadata_summary
)

logger = logging.getLogger("gemini-agent")

async def entrypoint(ctx: JobContext):
    async def write_transcript():
        try:
            if session is None:
                logger.warning("Session is None, cannot write transcript")
                return

            if session.history is None:
                logger.warning("Session history is None, cannot write transcript")
                return

            current_date=datetime.now().strftime("%y%m%d_%H%M%S")
            filename=f"/tmp/transcript_{ctx.room.name}_{current_date}.json"
            with open(filename, "w") as f:
                json.dump(session.history.to_dict(), f, indent=2)
            logger.info(f"Transcript written to {filename}")
        except Exception as e:
            logger.error(f"Failed to write transcript: {e}")

    ctx.add_shutdown_callback(write_transcript)
    session_start_time = time.time()
    await ctx.connect()

    role = "Software Developer"
    level = "Mid-Level"
    name = ""
    interview_id = None
    topics_to_cover = ""
    interviewer_questions = ""
    llm_provider = "google"  # Default to Google
    video_avatar = True  # Default to True
    interview_round = "Technical"  # Default to Technical
    company_info_available = """
Job Description: Software Developer (Mid-Level)

Requirements:
- 3-5 years of software development experience
- Strong proficiency in Python, JavaScript, or Java
- Experience with web frameworks (React, Angular, or Vue.js)
- Knowledge of databases (SQL and NoSQL)
- Experience with version control (Git)
- Understanding of software development lifecycle
- Problem-solving and analytical thinking skills
- Good communication and teamwork abilities

Responsibilities:
- Develop and maintain web applications
- Collaborate with cross-functional teams
- Write clean, maintainable code
- Participate in code reviews
- Debug and troubleshoot applications
- Stay updated with latest technologies
"""
    session = None
    gcp_manager = None
    egress_id = None

    try:
        # Wait for participant to join
        participant = await ctx.wait_for_participant()
        logger.info(f"Participant joined: {participant.identity}")

        # Extract and parse participant metadata using helper
        metadata = parse_participant_metadata(
            participant.metadata,
            default_role=role,
            default_level=level,
            default_name=name,
            default_interview_id=interview_id,
            default_topics_to_cover=topics_to_cover,
            default_interviewer_questions=interviewer_questions,
            default_company_info_available=company_info_available,
            default_llm_provider=llm_provider,
            default_video_avatar=video_avatar,
            default_interview_round=interview_round
        )

        # Update variables from parsed metadata
        role = metadata["role"]
        level = metadata["level"]
        name = metadata["name"]
        interview_id = metadata["interview_id"]
        topics_to_cover = metadata["topics_to_cover"]
        interviewer_questions = metadata["interviewer_questions"]
        company_info_available = metadata["company_info_available"]
        llm_provider = metadata.get("llm_provider", "google")
        video_avatar = metadata.get("video_avatar", True)
        interview_round = metadata.get("interview_round", "Technical")

        # Log metadata summary
        log_metadata_summary(metadata)

        # Initialize multi-agent interviewer
        logger.info("Initializing Multi-Agent Interview System...")
        agent_start_time = time.time()

        multi_agent_interviewer = MultiAgentInterviewer(
            role=role,
            level=level,
            name=name,
            topics_to_cover=topics_to_cover,
            interviewer_questions=interviewer_questions,
            company_info_available=company_info_available,
            llm_provider=llm_provider,
            interview_round=interview_round
        )

        # Get the initial agent (IntroductionAgent)
        agent = multi_agent_interviewer.get_initial_agent()

        agent_init_time = (time.time() - agent_start_time) * 1000
        logger.info(f"Multi-agent system initialization completed in {agent_init_time:.2f}ms")

        # Initialize agent session with event handlers
        logger.info("Starting agent session...")
        session = AgentSession()

        # Conditionally initialize Hedra avatar based on video_avatar setting
        hedra_avatar = None
        if video_avatar:
            logger.info("Video avatar enabled - initializing Hedra avatar...")
            avatar_image = Image.open(os.path.join(os.path.dirname(__file__), "assets/mary.png"))
            hedra_avatar = hedra.AvatarSession(avatar_image=avatar_image)
            await hedra_avatar.start(session, room=ctx.room)
            logger.info("Hedra avatar initialized successfully")
        else:
            logger.info("Video avatar disabled - skipping Hedra avatar initialization")

        # Set up the multi-agent system with the session and room
        multi_agent_interviewer.set_session(session)
        multi_agent_interviewer.set_room(ctx.room)

        # Add event handlers following LiveKit best practices
        setup_event_handlers(session)

        session_setup_start = time.time()
        await session.start(
            agent=agent,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                video_enabled=True,
                audio_enabled=True,
            ),
            room_output_options=RoomOutputOptions(
                transcription_enabled=True,
                audio_enabled=True,
            ),
        )
        session_setup_time = (time.time() - session_setup_start) * 1000
        logger.info(f"Session setup completed in {session_setup_time:.2f}ms")
        logger.info("Multi-agent session started successfully")

        # Start the interview timer
        multi_agent_interviewer.start_interview_timer()

        # Set up silence monitoring, timing enforcement, and automatic phase transitions
        await multi_agent_interviewer.start_silence_monitoring()
        await multi_agent_interviewer.start_timing_monitoring()
        await multi_agent_interviewer.start_phase_transition_monitoring()

        # Give the session a moment to fully initialize
        await asyncio.sleep(3)

       

        # Initialize continuous storage after agent is ready
        logger.info("Initializing continuous storage...")
        gcp_manager = GCPEgressManager()

        # Generate recording filename using helper
        filename_prefix = get_recording_filename_prefix(interview_id, ctx.room.name)
        recording_start_time = time.time()
        egress_id = await gcp_manager.start_room_recording(
            room_name=ctx.room.name,
            layout="grid",
            filename_prefix=filename_prefix,
        )
        recording_setup_time = (time.time() - recording_start_time) * 1000
        logger.info(f"Recording setup completed in {recording_setup_time:.2f}ms")
        logger.info(f"Continuous recording started with egress ID: {egress_id}")

        # Track total setup time
        total_setup_time = (time.time() - session_start_time) * 1000
        logger.info(f"Total multi-agent setup completed in {total_setup_time:.2f}ms")

        # Log the multi-agent system status
        agent_info = multi_agent_interviewer.get_agent_info()
        logger.info(f"Multi-agent system status: {agent_info}")

        # Keep the session alive - following LiveKit best practices
        # The session will automatically end when participants leave
        # Event handlers will manage state transitions and errors
        # The multi-agent system will handle phase transitions automatically

    except Exception as e:
        logger.error(f"Error in multi-agent entrypoint: {e}")
        await cleanup_resources(session, gcp_manager, egress_id)
        raise





if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
