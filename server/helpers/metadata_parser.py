"""
Metadata parsing utilities for LiveKit participant metadata.
Handles JSON parsing with fallbacks and validation.
"""

import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger("metadata-parser")


def parse_participant_metadata(
    metadata: Optional[str],
    default_role: str = "Software Developer",
    default_level: str = "Mid-Level",
    default_name: str = "",
    default_interview_id: Optional[str] = None,
    default_topics_to_cover: str = "",
    default_interviewer_questions: str = "",
    default_company_info_available: str = "",
    default_llm_provider: str = "google",
    default_video_avatar: bool = False,
    default_interview_round: str = "Technical"
) -> Dict[str, Any]:
    """
    Parse participant metadata with fallbacks.

    Args:
        metadata: JSON string from participant metadata
        default_role: Default role if not found in metadata
        default_level: Default level if not found in metadata
        default_name: Default name if not found in metadata
        default_interview_id: Default interview ID if not found in metadata
        default_topics_to_cover: Default topics to cover if not found in metadata
        default_interviewer_questions: Default interviewer questions if not found in metadata
        default_company_info_available: Default company info (job description) if not found in metadata
        default_llm_provider: Default LLM provider if not found in metadata ("google" or "openai")
        default_video_avatar: Default video avatar setting if not found in metadata (True/False)
        default_interview_round: Default interview round if not found in metadata ("HR", "Technical", "Managerial", "Architect", etc.)

    Returns:
        Dictionary with parsed metadata values
    """
    result = {
        "role": default_role,
        "level": default_level,
        "name": default_name,
        "interview_id": default_interview_id,
        "topics_to_cover": default_topics_to_cover,
        "interviewer_questions": default_interviewer_questions,
        "company_info_available": default_company_info_available,
        "llm_provider": default_llm_provider,
        "video_avatar": default_video_avatar,
        "interview_round": default_interview_round
    }
    
    if not metadata:
        logger.info("No participant metadata found, using defaults")
        return result
    
    try:
        parsed_metadata = json.loads(metadata)
        logger.debug(f"Raw metadata: {parsed_metadata}")
        
        # Extract and validate each field
        if "role" in parsed_metadata:
            role = parsed_metadata["role"]
            if isinstance(role, str) and role.strip():
                result["role"] = role.strip()
                logger.debug(f"Extracted role: {result['role']}")
            else:
                logger.warning(f"Invalid role in metadata: {role}, using default")
        
        if "level" in parsed_metadata:
            level = parsed_metadata["level"]
            if isinstance(level, str) and level.strip():
                result["level"] = level.strip()
                logger.debug(f"Extracted level: {result['level']}")
            else:
                logger.warning(f"Invalid level in metadata: {level}, using default")
        
        if "name" in parsed_metadata:
            name = parsed_metadata["name"]
            if isinstance(name, str):
                result["name"] = name.strip()
                logger.debug(f"Extracted name: {result['name']}")
            else:
                logger.warning(f"Invalid name in metadata: {name}, using default")
        
        if "interview_id" in parsed_metadata:
            interview_id = parsed_metadata["interview_id"]
            if isinstance(interview_id, str) and interview_id.strip():
                result["interview_id"] = interview_id.strip()
                logger.debug(f"Extracted interview_id: {result['interview_id']}")
            else:
                logger.warning(f"Invalid interview_id in metadata: {interview_id}, using default")

        if "topics_to_cover" in parsed_metadata:
            topics_to_cover = parsed_metadata["topics_to_cover"]
            if isinstance(topics_to_cover, str):
                result["topics_to_cover"] = topics_to_cover.strip()
                logger.debug(f"Extracted topics_to_cover: {result['topics_to_cover']}")
            else:
                logger.warning(f"Invalid topics_to_cover in metadata: {topics_to_cover}, using default")

        if "interviewer_questions" in parsed_metadata:
            interviewer_questions = parsed_metadata["interviewer_questions"]
            if isinstance(interviewer_questions, str):
                result["interviewer_questions"] = interviewer_questions.strip()
                logger.debug(f"Extracted interviewer_questions: {result['interviewer_questions']}")
            else:
                logger.warning(f"Invalid interviewer_questions in metadata: {interviewer_questions}, using default")

        if "company_info_available" in parsed_metadata:
            company_info_available = parsed_metadata["company_info_available"]
            if isinstance(company_info_available, str):
                result["company_info_available"] = company_info_available.strip()
                logger.debug(f"Extracted company_info_available: {result['company_info_available'][:100]}...")
            elif isinstance(company_info_available, bool):
                # Handle legacy boolean format
                result["company_info_available"] = "" if not company_info_available else "Job description available"
                logger.debug(f"Extracted company_info_available (legacy boolean): {result['company_info_available']}")
            else:
                logger.warning(f"Invalid company_info_available in metadata: {company_info_available}, using default")

        if "llm_provider" in parsed_metadata:
            llm_provider = parsed_metadata["llm_provider"]
            if isinstance(llm_provider, str) and llm_provider.strip().lower() in ["google", "openai"]:
                result["llm_provider"] = llm_provider.strip().lower()
                logger.debug(f"Extracted llm_provider: {result['llm_provider']}")
            else:
                logger.warning(f"Invalid llm_provider in metadata: {llm_provider}, using default (google)")

        if "video_avatar" in parsed_metadata:
            video_avatar = parsed_metadata["video_avatar"]
            if isinstance(video_avatar, bool):
                result["video_avatar"] = video_avatar
                logger.debug(f"Extracted video_avatar: {result['video_avatar']}")
            elif isinstance(video_avatar, str) and video_avatar.strip().lower() in ["true", "false"]:
                result["video_avatar"] = video_avatar.strip().lower() == "true"
                logger.debug(f"Extracted video_avatar (from string): {result['video_avatar']}")
            else:
                logger.warning(f"Invalid video_avatar in metadata: {video_avatar}, using default (True)")

        if "interview_round" in parsed_metadata:
            interview_round = parsed_metadata["interview_round"]
            if isinstance(interview_round, str) and interview_round.strip():
                # Define valid interview rounds (expandable for future additions)
                valid_rounds = ["hr", "technical", "managerial", "final", "admission", "screening", "behavioral", "architect", "system-design", "other"]
                round_lower = interview_round.strip().lower()
                if round_lower in valid_rounds:
                    result["interview_round"] = interview_round.strip().title()
                    logger.debug(f"Extracted interview_round: {result['interview_round']}")
                else:
                    # Allow any string value but log it for awareness
                    result["interview_round"] = interview_round.strip().title()
                    logger.debug(f"Extracted custom interview_round: {result['interview_round']}")
            else:
                logger.warning(f"Invalid interview_round in metadata: {interview_round}, using default (Technical)")

        logger.info(f"Parsed metadata - Role: {result['role']}, Level: {result['level']}, Name: {result['name']}")
        logger.info(f"LLM Provider: {result['llm_provider']}, Topics: {result['topics_to_cover'] or 'None'}")
        logger.info(f"Interviewer questions: {result['interviewer_questions'] or 'None'}, Company info: {result['company_info_available']}")
        logger.info(f"Video Avatar: {result['video_avatar']}, Interview Round: {result['interview_round']}")
        
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse metadata JSON: {e}, using defaults")
    except Exception as e:
        logger.error(f"Unexpected error parsing metadata: {e}, using defaults")
    
    return result


def validate_metadata_fields(metadata: Dict[str, Any]) -> Dict[str, bool]:
    """
    Validate metadata fields and return validation results.

    Args:
        metadata: Parsed metadata dictionary

    Returns:
        Dictionary with validation results for each field
    """
    validation_results = {
        "role_valid": False,
        "level_valid": False,
        "name_valid": False,
        "interview_id_valid": False,
        "topics_to_cover_valid": False,
        "interviewer_questions_valid": False,
        "company_info_available_valid": False,
        "llm_provider_valid": False,
        "video_avatar_valid": False,
        "interview_round_valid": False
    }

    # Validate role
    role = metadata.get("role", "")
    if isinstance(role, str) and role.strip():
        validation_results["role_valid"] = True

    # Validate level
    level = metadata.get("level", "")
    if isinstance(level, str) and level.strip():
        validation_results["level_valid"] = True

    # Validate name (can be empty)
    name = metadata.get("name", "")
    if isinstance(name, str):
        validation_results["name_valid"] = True

    # Validate interview_id (can be None)
    interview_id = metadata.get("interview_id")
    if interview_id is None or (isinstance(interview_id, str) and interview_id.strip()):
        validation_results["interview_id_valid"] = True

    # Validate topics_to_cover (can be empty)
    topics_to_cover = metadata.get("topics_to_cover", "")
    if isinstance(topics_to_cover, str):
        validation_results["topics_to_cover_valid"] = True

    # Validate interviewer_questions (can be empty)
    interviewer_questions = metadata.get("interviewer_questions", "")
    if isinstance(interviewer_questions, str):
        validation_results["interviewer_questions_valid"] = True

    # Validate company_info_available
    company_info_available = metadata.get("company_info_available", False)
    if isinstance(company_info_available, bool):
        validation_results["company_info_available_valid"] = True

    # Validate video_avatar
    video_avatar = metadata.get("video_avatar", True)
    if isinstance(video_avatar, bool):
        validation_results["video_avatar_valid"] = True

    # Validate interview_round
    interview_round = metadata.get("interview_round", "Technical")
    if isinstance(interview_round, str) and interview_round.strip():
        validation_results["interview_round_valid"] = True

    return validation_results


def get_recording_filename_prefix(interview_id: Optional[str], room_name: str) -> str:
    """
    Generate recording filename prefix based on interview ID or room name.
    
    Args:
        interview_id: Interview ID from metadata
        room_name: Room name as fallback
    
    Returns:
        Filename prefix for recording
    """
    if interview_id and interview_id.strip():
        prefix = f"interview-ai/{interview_id.strip()}"
        logger.debug(f"Using interview ID for filename: {prefix}")
    else:
        prefix = f"interview-ai/{room_name}-recording"
        logger.debug(f"Using room name for filename: {prefix}")
    
    return prefix


def log_metadata_summary(metadata: Dict[str, Any]):
    """
    Log a summary of the parsed metadata.

    Args:
        metadata: Parsed metadata dictionary
    """
    logger.info("=" * 40)
    logger.info("PARTICIPANT METADATA SUMMARY")
    logger.info("=" * 40)
    logger.info(f"Role: {metadata.get('role', 'N/A')}")
    logger.info(f"Level: {metadata.get('level', 'N/A')}")
    logger.info(f"Name: {metadata.get('name', 'Anonymous')}")
    logger.info(f"Interview ID: {metadata.get('interview_id', 'N/A')}")
    logger.info(f"Interview Round: {metadata.get('interview_round', 'Technical')}")
    logger.info(f"Topics to Cover: {metadata.get('topics_to_cover', 'None specified')}")
    logger.info(f"Interviewer Questions: {metadata.get('interviewer_questions', 'None specified')}")
    logger.info(f"Company Info Available: {metadata.get('company_info_available', False)}")
    logger.info(f"Video Avatar: {metadata.get('video_avatar', True)}")

    # Validation summary
    validation = validate_metadata_fields(metadata)
    valid_fields = sum(validation.values())
    total_fields = len(validation)

    logger.info(f"Validation: {valid_fields}/{total_fields} fields valid")
    logger.info("=" * 40)
