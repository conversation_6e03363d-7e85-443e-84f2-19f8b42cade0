"""
Google Cloud Platform storage integration for LiveKit egress recordings and transcript storage.
Handles starting and managing room composite egress with GCP storage, and uploading transcripts.
"""

import os
import logging
import json
from typing import Optional
from datetime import datetime
from google.cloud import storage
from livekit import api as lkapi
from livekit.api import EncodingOptionsPreset
from dotenv import load_dotenv

load_dotenv()

def create_recorder_token(room_name: str):

    token = lkapi.AccessToken(os.getenv('LIVEKIT_API_KEY'), os.getenv('LIVEKIT_API_SECRET')) \
    .with_identity("identity") \
    .with_name("recorder") \
    .with_grants(lkapi.VideoGrants(
        room_join=True,
        room=room_name,
         can_publish=False,
        can_publish_data=False

    ))
    return token.to_jwt()
  

# Example usage


logger = logging.getLogger("gcp-storage")


class GCPBaseManager:
    """Base class for GCP storage operations with shared functionality."""

    def __init__(self, credentials_file_path: str = "./credentials.json"):
        """Initialize base GCP manager with common setup."""
        self.credentials_file_path = credentials_file_path
        self.bucket_name = os.getenv("GCP_BUCKET")

        if not self.bucket_name:
            raise ValueError("Missing required GCP_BUCKET environment variable")

        logger.info(f"GCP Base Manager initialized - Bucket: {self.bucket_name}")

    def _load_gcp_credentials(self) -> str:
        """Load GCP credentials from JSON file as string."""
        try:
            with open(self.credentials_file_path, "r") as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"GCP credentials file not found: {self.credentials_file_path}")
        except Exception as e:
            raise Exception(f"Error reading GCP credentials: {e}")

    def _validate_credentials_file(self) -> bool:
        """Validate that credentials file exists and is readable."""
        try:
            self._load_gcp_credentials()
            return True
        except Exception as e:
            logger.error(f"Credentials validation failed: {e}")
            return False


class GCPEgressManager(GCPBaseManager):
    """Manages LiveKit egress recordings with GCP storage backend for continuous recording."""

    def __init__(self, credentials_file_path: str = "./credentials.json"):
        """Initialize the GCP egress manager with environment variables."""
        super().__init__(credentials_file_path)

        self.api_url = os.getenv("LIVEKIT_URL")
        self.api_key = os.getenv("LIVEKIT_API_KEY")
        self.api_secret = os.getenv("LIVEKIT_API_SECRET")
        self.bucket = self.bucket_name  # Use inherited bucket_name
        self.active_recordings = {}  # Track active recordings by room

        logger.info("Initializing GCP Egress Manager for continuous storage")

        # Validate required LiveKit environment variables
        if not all([self.api_url, self.api_key, self.api_secret]):
            raise ValueError(
                f"Missing required LiveKit environment variables. Found: "
                f"URL={bool(self.api_url)}, KEY={bool(self.api_key)}, SECRET={bool(self.api_secret)}"
            )

        logger.info(f"GCP Egress Manager initialized - Bucket: {self.bucket}")

    async def start_room_recording(
        self,
        room_name: str,
        layout: str = "speaker",
        preset: EncodingOptionsPreset = EncodingOptionsPreset.H264_720P_30,
        audio_only: bool = False,
        segment_duration: int = 60,
        filename_prefix: Optional[str] = None,
    ) -> str:
        """
        Start continuous room composite egress recording to GCP storage.

        Args:
            room_name: Name of the room to record
            layout: Layout type ("speaker" or "grid")
            preset: Video encoding preset
            audio_only: Whether to record audio only
            segment_duration: Duration of each segment in seconds
            filename_prefix: Custom filename prefix (defaults to room name)
        Returns:
            Egress ID of the started recording

        Raises:
            Exception: If egress fails to start
        """




        # Check if recording already exists for this room
        if room_name in self.active_recordings:
            logger.warning(f"Recording already active for room {room_name}")
            return self.active_recordings[room_name]

        playlist_name = f"{filename_prefix}.m3u8"
        live_playlist_name = f"interview-ai.m3u8"

        # Load GCP credentials
        gcp_creds = self._load_gcp_credentials()
        token = create_recorder_token("my-interview-room")
        print(token)
        custom_base_url = (
    f"http://localhost:3000/lk-recording-view"
    f"?token={token}&id=recorder&isRecorder=true"
)

        # Create optimized egress request for continuous recording
        egress_request = lkapi.RoomCompositeEgressRequest(
            room_name=room_name,
            layout=layout,
            custom_base_url=custom_base_url,
            preset=preset,
            audio_only=audio_only,
            segment_outputs=[
                lkapi.SegmentedFileOutput(
                    filename_prefix=filename_prefix,
                    playlist_name=playlist_name,
                    live_playlist_name=live_playlist_name,
                    segment_duration=segment_duration,
                    gcp=lkapi.GCPUpload(
                        credentials=gcp_creds,
                        bucket=self.bucket,
                    ),
                )
            ]
        )

        api_client = lkapi.LiveKitAPI(self.api_url, self.api_key, self.api_secret)

        try:
            logger.info(f"Starting continuous recording for room: {room_name}")
            logger.info(f"Recording configuration - Layout: {layout}, Preset: {preset}, Segments: {segment_duration}s")

            response = await api_client.egress.start_room_composite_egress(egress_request)
            egress_id = response.egress_id

            # Track the active recording
            self.active_recordings[room_name] = egress_id

            logger.info(f"Continuous recording started successfully - Egress ID: {egress_id}")
            logger.info(f"Recording will be saved to GCP bucket: {self.bucket}/{filename_prefix}")



            return egress_id

        except Exception as e:
            logger.error(f"Failed to start continuous recording for room {room_name}: {e}")



            raise
        finally:
            await api_client.aclose()

    async def stop_recording(self, egress_id: str) -> bool:
        """
        Stop an active continuous recording.

        Args:
            egress_id: ID of the egress to stop

        Returns:
            True if successfully stopped, False otherwise
        """


        api_client = lkapi.LiveKitAPI(self.api_url, self.api_key, self.api_secret)

        try:
            logger.info(f"Stopping continuous recording: {egress_id}")
            request = lkapi.StopEgressRequest(egress_id=egress_id)
            await api_client.egress.stop_egress(request)

            # Remove from active recordings tracking
            room_to_remove = None
            for room_name, active_egress_id in self.active_recordings.items():
                if active_egress_id == egress_id:
                    room_to_remove = room_name
                    break

            if room_to_remove:
                del self.active_recordings[room_to_remove]
                logger.info(f"Removed recording tracking for room: {room_to_remove}")

            logger.info(f"Continuous recording stopped successfully: {egress_id}")



            return True

        except Exception as e:
            logger.error(f"Failed to stop continuous recording {egress_id}: {e}")



            return False
        finally:
            await api_client.aclose()

    async def stop_room_recording(self, room_name: str) -> bool:
        """
        Stop recording for a specific room by room name.

        Args:
            room_name: Name of the room to stop recording

        Returns:
            True if successfully stopped, False otherwise
        """
        if room_name not in self.active_recordings:
            logger.warning(f"No active recording found for room: {room_name}")
            return False

        egress_id = self.active_recordings[room_name]
        return await self.stop_recording(egress_id)

    async def get_recording_status(self, egress_id: str) -> Optional[dict]:
        """
        Get the status of an egress recording.

        Args:
            egress_id: ID of the egress to check

        Returns:
            Dictionary with egress status information, or None if not found
        """
        api_client = lkapi.LiveKitAPI(self.api_url, self.api_key, self.api_secret)

        try:
            response = await api_client.egress.list_egress(lkapi.ListEgressRequest())
            egress_list = response.items if hasattr(response, 'items') else []

            for egress in egress_list:
                if egress.egress_id == egress_id:
                    return {
                        'egress_id': egress.egress_id,
                        'room_name': egress.room_name,
                        'status': egress.status,
                        'started_at': egress.started_at,
                        'ended_at': getattr(egress, 'ended_at', None)
                    }

            return None

        except Exception as e:
            logger.error(f"Failed to get egress status for {egress_id}: {e}")
            return None
        finally:
            await api_client.aclose()

    def get_active_recordings(self) -> dict:
        """
        Get all currently active recordings.

        Returns:
            Dictionary mapping room names to egress IDs
        """
        return self.active_recordings.copy()

    def is_recording_active(self, room_name: str) -> bool:
        """
        Check if recording is active for a specific room.

        Args:
            room_name: Name of the room to check

        Returns:
            True if recording is active, False otherwise
        """
        return room_name in self.active_recordings


class GCPTranscriptStorage(GCPBaseManager):
    """Handles uploading interview transcripts to Google Cloud Storage."""

    def __init__(self, credentials_file_path: str = "./credentials.json"):
        """Initialize the GCP transcript storage manager."""
        super().__init__(credentials_file_path)

        # Initialize GCP Storage client
        try:
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = self.credentials_file_path
            self.client = storage.Client()
            self.bucket = self.client.bucket(self.bucket_name)
            logger.info(f"GCP Transcript Storage initialized - Bucket: {self.bucket_name}")
        except Exception as e:
            logger.error(f"Failed to initialize GCP Storage client: {e}")
            raise

    async def save_transcript(
        self,
        transcript_content: str,
        room_name: str,
        interview_id: str = None,
        candidate_name: str = "",
        role: str = "",
        level: str = "",
        interview_round: str = "",
        folder_prefix: str = "session-transcripts"
    ) -> str:
        """
        Save interview transcript to GCP Cloud Storage.

        Args:
            transcript_content: The formatted transcript content
            room_name: Name of the interview room
            candidate_name: Name of the candidate (optional)
            role: Job role being interviewed for (optional)
            level: Experience level (optional)
            interview_round: Type of interview round (optional)
            folder_prefix: Folder prefix in the bucket (default: "transcripts")

        Returns:
            GCS path of the uploaded transcript

        Raises:
            Exception: If upload fails
        """
        try:
            # Import the helper function
            from helpers.metadata_parser import get_recording_filename_prefix

            # Generate timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Use the same filename prefix logic as video recordings
            filename_prefix = get_recording_filename_prefix(interview_id, room_name)

            # Create transcript filename with timestamp
            filename = f"{filename_prefix}.txt"

            # Create the full GCS path (session-transcripts folder)
            gcs_path = f"{folder_prefix}/{filename}"

            # Create metadata for the transcript
            metadata = {
                "room_name": room_name,
                "candidate_name": candidate_name,
                "role": role,
                "level": level,
                "interview_round": interview_round,
                "upload_timestamp": datetime.now().isoformat(),
                "content_type": "interview_transcript"
            }

            # Upload to GCS
            blob = self.bucket.blob(gcs_path)
            blob.metadata = metadata

            # Upload the transcript content
            blob.upload_from_string(
                transcript_content,
                content_type='text/plain; charset=utf-8'
            )

            logger.info(f"Transcript uploaded successfully to GCS: gs://{self.bucket_name}/{gcs_path}")
            logger.info(f"Transcript metadata: {metadata}")

            return f"gs://{self.bucket_name}/{gcs_path}"

        except Exception as e:
            logger.error(f"Failed to upload transcript to GCS: {e}")
            raise

    async def save_transcript_json(
        self,
        transcript_data: dict,
        room_name: str,
        interview_id: str = None,
        candidate_name: str = "",
        role: str = "",
        level: str = "",
        interview_round: str = "",
        folder_prefix: str = "session-transcripts"
    ) -> str:
        """
        Save interview transcript as JSON to GCP Cloud Storage.

        Args:
            transcript_data: Dictionary containing transcript entries and metadata
            room_name: Name of the interview room
            candidate_name: Name of the candidate (optional)
            role: Job role being interviewed for (optional)
            level: Experience level (optional)
            interview_round: Type of interview round (optional)
            folder_prefix: Folder prefix in the bucket (default: "transcripts")

        Returns:
            GCS path of the uploaded transcript
        """
        try:
            # Import the helper function
            from helpers.metadata_parser import get_recording_filename_prefix

            # Generate timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Use the same filename prefix logic as video recordings
            filename_prefix = get_recording_filename_prefix(interview_id, room_name)

            # Create transcript filename with timestamp
            filename = f"{filename_prefix}.json"

            # Create the full GCS path (session-transcripts folder)
            gcs_path = f"{folder_prefix}/{filename}"

            # Add metadata to the transcript data
            enhanced_data = {
                **transcript_data,
                "metadata": {
                    "room_name": room_name,
                    "candidate_name": candidate_name,
                    "role": role,
                    "level": level,
                    "interview_round": interview_round,
                    "upload_timestamp": datetime.now().isoformat(),
                    "content_type": "interview_transcript_json"
                }
            }

            # Upload to GCS
            blob = self.bucket.blob(gcs_path)
            blob.metadata = enhanced_data["metadata"]

            # Upload the JSON content
            blob.upload_from_string(
                json.dumps(enhanced_data, indent=2, ensure_ascii=False),
                content_type='application/json; charset=utf-8'
            )

            logger.info(f"JSON transcript uploaded successfully to GCS: gs://{self.bucket_name}/{gcs_path}")

            return f"gs://{self.bucket_name}/{gcs_path}"

        except Exception as e:
            logger.error(f"Failed to upload JSON transcript to GCS: {e}")
            raise

    def list_transcripts(self, folder_prefix: str = "transcripts", limit: int = 100) -> list:
        """
        List available transcripts in GCS.

        Args:
            folder_prefix: Folder prefix to search in
            limit: Maximum number of transcripts to return

        Returns:
            List of transcript blob information
        """
        try:
            blobs = self.client.list_blobs(
                self.bucket_name,
                prefix=folder_prefix,
                max_results=limit
            )

            transcript_list = []
            for blob in blobs:
                if blob.name.endswith(('.txt', '.json')):
                    transcript_info = {
                        "name": blob.name,
                        "size": blob.size,
                        "created": blob.time_created.isoformat() if blob.time_created else None,
                        "updated": blob.updated.isoformat() if blob.updated else None,
                        "metadata": blob.metadata or {},
                        "gcs_path": f"gs://{self.bucket_name}/{blob.name}"
                    }
                    transcript_list.append(transcript_info)

            logger.info(f"Found {len(transcript_list)} transcripts in {folder_prefix}")
            return transcript_list

        except Exception as e:
            logger.error(f"Failed to list transcripts: {e}")
            return []

    async def download_transcript(self, gcs_path: str) -> str:
        """
        Download transcript content from GCS.

        Args:
            gcs_path: Full GCS path (gs://bucket/path/file.txt)

        Returns:
            Transcript content as string
        """
        try:
            # Extract blob name from GCS path
            blob_name = gcs_path.replace(f"gs://{self.bucket_name}/", "")
            blob = self.bucket.blob(blob_name)

            content = blob.download_as_text(encoding='utf-8')
            logger.info(f"Downloaded transcript from GCS: {gcs_path}")

            return content

        except Exception as e:
            logger.error(f"Failed to download transcript from GCS: {e}")
            raise
