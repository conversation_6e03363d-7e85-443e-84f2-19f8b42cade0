# GCP Transcript Storage

This document explains how to use the Google Cloud Platform (GCP) Cloud Storage integration for saving interview transcripts instead of temporary local files.

## Overview

The system now supports saving interview transcripts to Google Cloud Storage, providing:
- **Persistent storage**: Transcripts are saved permanently in the cloud
- **Organized structure**: Transcripts are organized by date and metadata
- **Multiple formats**: Both human-readable text and structured JSON formats
- **Rich metadata**: Includes candidate info, role, interview round, etc.
- **Easy retrieval**: Command-line tools for listing, searching, and downloading

## Setup

### 1. Environment Variables

Add the following to your `.env` file:

```bash
# GCP Storage Bucket for Recordings and Transcripts
GCP_BUCKET=your-gcp-bucket-name
```

### 2. GCP Credentials

Ensure you have a `credentials.json` file in the server directory with your GCP service account credentials.

The service account needs the following permissions:
- **Storage Object Admin** (for full read/write access to the bucket)
- Or minimum: **Storage Object Creator** + **Storage Object Viewer**

### 3. Install Dependencies

The required dependency is already added to `requirements.txt`:

```bash
pip install google-cloud-storage
```

## How It Works

### Automatic Transcript Saving

When an interview completes, the system automatically:

1. **Collects transcript data** from the multi-agent interview system
2. **Saves in two formats**:
   - **Text format**: Human-readable transcript with timestamps
   - **JSON format**: Structured data with metadata and detailed entries
3. **Organizes by date**: Files are stored in folders like `transcripts/********/`
4. **Includes metadata**: Candidate name, role, interview round, etc.
5. **Fallback to local**: If GCP fails, saves to `/tmp/` as backup

### File Structure in GCS

```
your-gcp-bucket/
├── transcripts/
│   ├── ********/
│   │   ├── transcript_room123_********_143022_candidate_John_Doe_role_Software_Engineer.txt
│   │   ├── transcript_room123_********_143022_candidate_John_Doe_role_Software_Engineer.json
│   │   └── ...
│   └── 20241129/
│       └── ...
└── session_transcripts/
    ├── ********/
    │   └── transcript_room456_********_150000.json
    └── ...
```

## Usage

### Command-Line Tools

#### List All Transcripts

```bash
python transcript_manager.py list
```

#### List Limited Number

```bash
python transcript_manager.py list --limit 20
```

#### Download Specific Transcript

```bash
python transcript_manager.py download gs://your-bucket/transcripts/********/transcript_room123_********_143022.txt
```

#### Search by Candidate

```bash
python transcript_manager.py search --candidate "John Doe"
```

#### Search by Role

```bash
python transcript_manager.py search --role "Software Engineer"
```

#### Search by Date

```bash
python transcript_manager.py search --date "********"
```

#### Combined Search

```bash
python transcript_manager.py search --candidate "John" --role "Engineer" --date "********"
```

### Programmatic Usage

```python
from storage import GCPTranscriptStorage

# Initialize storage
storage = GCPTranscriptStorage()

# Save a text transcript
gcs_path = await storage.save_transcript(
    transcript_content="[00:00] Interviewer: Hello...",
    room_name="room123",
    candidate_name="John Doe",
    role="Software Engineer",
    level="Senior",
    interview_round="Technical"
)

# Save JSON transcript with detailed data
transcript_data = {
    "transcript_entries": [...],
    "interview_info": {...}
}

gcs_path = await storage.save_transcript_json(
    transcript_data=transcript_data,
    room_name="room123",
    candidate_name="John Doe",
    role="Software Engineer"
)

# List transcripts
transcripts = storage.list_transcripts(limit=50)

# Download transcript
content = await storage.download_transcript(gcs_path)
```

## Testing

### Test Your Setup

Run the test script to verify everything is working:

```bash
cd server
python test_gcp_transcript.py
```

This will:
- Check environment variables and credentials
- Test uploading both text and JSON formats
- Test listing and downloading transcripts
- Verify the complete workflow

### Expected Output

```
🧪 Testing GCP Transcript Storage...
==================================================
1. Checking environment variables...
✅ GCP_BUCKET: your-bucket-name

2. Checking GCP credentials...
✅ credentials.json file found

3. Initializing GCP Transcript Storage...
✅ GCP Transcript Storage initialized successfully

4. Testing text transcript upload...
✅ Text transcript uploaded: gs://your-bucket/test_transcripts/...

5. Testing JSON transcript upload...
✅ JSON transcript uploaded: gs://your-bucket/test_transcripts/...

6. Testing transcript listing...
✅ Found 2 test transcript(s)

7. Testing transcript download...
✅ Downloaded transcript (XXX characters)
✅ Downloaded content matches uploaded content

🎉 All tests passed! GCP Transcript Storage is working correctly.
```

## Troubleshooting

### Common Issues

1. **"Missing required GCP_BUCKET environment variable"**
   - Add `GCP_BUCKET=your-bucket-name` to your `.env` file

2. **"GCP credentials file not found"**
   - Ensure `credentials.json` exists in the server directory
   - Download it from Google Cloud Console → IAM & Admin → Service Accounts

3. **"Permission denied" errors**
   - Ensure your service account has Storage Object Admin permissions
   - Check that the bucket exists and is accessible

4. **"Failed to initialize GCP Storage client"**
   - Verify your credentials.json file is valid JSON
   - Check that the service account is active

### Fallback Behavior

If GCP storage fails, the system automatically falls back to saving transcripts in `/tmp/` directory with detailed error logging. This ensures interviews can continue even if cloud storage is temporarily unavailable.

## Benefits

- **Persistent Storage**: No more lost transcripts from temporary files
- **Organized**: Easy to find transcripts by date, candidate, or role
- **Searchable**: Rich metadata makes transcripts discoverable
- **Scalable**: Cloud storage handles large volumes of transcripts
- **Accessible**: Download transcripts from anywhere with proper credentials
- **Backup**: Automatic fallback ensures no data loss

## Integration Points

The GCP transcript storage is integrated into:

1. **Multi-Agent Interviewer** (`agents/multi_agent_interviewer.py`)
   - Saves complete interview transcripts when interview ends
   - Includes all metadata and conversation history

2. **Main Agent** (`agent.py`)
   - Saves session-level transcripts
   - Handles LiveKit session history

3. **Storage Module** (`storage/gcp.py`)
   - Core GCP integration
   - Handles uploads, downloads, and listing

This provides comprehensive transcript storage across all interview types and formats.
