#!/usr/bin/env python3
"""
Transcript Manager - Utility script for managing interview transcripts in GCP Cloud Storage.

This script provides command-line tools to:
- List available transcripts
- Download specific transcripts
- View transcript metadata
- Search transcripts by candidate name, role, or date

Usage:
    python transcript_manager.py list                    # List all transcripts
    python transcript_manager.py list --limit 20        # List last 20 transcripts
    python transcript_manager.py download <gcs_path>     # Download specific transcript
    python transcript_manager.py search --candidate "<PERSON>"  # Search by candidate
    python transcript_manager.py search --role "Software Engineer"  # Search by role
    python transcript_manager.py search --date "20241128"  # Search by date
"""

import asyncio
import argparse
import sys
import os
from datetime import datetime
from storage import GCPTranscriptStorage


async def list_transcripts(limit: int = 100):
    """List available transcripts in GCP Cloud Storage."""
    try:
        storage = GCPTranscriptStorage()
        transcripts = storage.list_transcripts(limit=limit)
        
        if not transcripts:
            print("No transcripts found in GCP Cloud Storage.")
            return
        
        print(f"\n📋 Found {len(transcripts)} transcript(s):\n")
        print(f"{'#':<3} {'Date':<12} {'Candidate':<20} {'Role':<25} {'Round':<15} {'Size':<8} {'GCS Path'}")
        print("-" * 120)
        
        for i, transcript in enumerate(transcripts, 1):
            metadata = transcript.get('metadata', {})
            candidate = metadata.get('candidate_name', 'Unknown')[:19]
            role = metadata.get('role', 'Unknown')[:24]
            round_type = metadata.get('interview_round', 'Unknown')[:14]
            
            # Extract date from created timestamp
            created = transcript.get('created', '')
            if created:
                try:
                    date_obj = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    date_str = date_obj.strftime('%Y-%m-%d')
                except:
                    date_str = 'Unknown'
            else:
                date_str = 'Unknown'
            
            size_kb = f"{transcript.get('size', 0) // 1024}KB"
            gcs_path = transcript.get('gcs_path', '')
            
            print(f"{i:<3} {date_str:<12} {candidate:<20} {role:<25} {round_type:<15} {size_kb:<8} {gcs_path}")
        
        print(f"\n💡 Use 'python transcript_manager.py download <gcs_path>' to download a specific transcript")
        
    except Exception as e:
        print(f"❌ Error listing transcripts: {e}")
        sys.exit(1)


async def download_transcript(gcs_path: str):
    """Download and display a specific transcript."""
    try:
        storage = GCPTranscriptStorage()
        content = await storage.download_transcript(gcs_path)
        
        print(f"\n📄 Transcript from: {gcs_path}")
        print("=" * 80)
        print(content)
        print("=" * 80)
        
        # Ask if user wants to save locally
        save_local = input("\n💾 Save transcript locally? (y/N): ").lower().strip()
        if save_local == 'y':
            # Extract filename from GCS path
            filename = gcs_path.split('/')[-1]
            local_path = f"./downloaded_{filename}"
            
            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Transcript saved to: {local_path}")
        
    except Exception as e:
        print(f"❌ Error downloading transcript: {e}")
        sys.exit(1)


async def search_transcripts(candidate: str = None, role: str = None, date: str = None, limit: int = 100):
    """Search transcripts by various criteria."""
    try:
        storage = GCPTranscriptStorage()
        transcripts = storage.list_transcripts(limit=limit)
        
        # Filter transcripts based on search criteria
        filtered = []
        for transcript in transcripts:
            metadata = transcript.get('metadata', {})
            
            # Check candidate name
            if candidate and candidate.lower() not in metadata.get('candidate_name', '').lower():
                continue
            
            # Check role
            if role and role.lower() not in metadata.get('role', '').lower():
                continue
            
            # Check date (in GCS path or created date)
            if date:
                gcs_path = transcript.get('gcs_path', '')
                created = transcript.get('created', '')
                if date not in gcs_path and date not in created:
                    continue
            
            filtered.append(transcript)
        
        if not filtered:
            print("🔍 No transcripts found matching the search criteria.")
            return
        
        print(f"\n🔍 Found {len(filtered)} transcript(s) matching search criteria:\n")
        
        # Display results in same format as list command
        print(f"{'#':<3} {'Date':<12} {'Candidate':<20} {'Role':<25} {'Round':<15} {'Size':<8} {'GCS Path'}")
        print("-" * 120)
        
        for i, transcript in enumerate(filtered, 1):
            metadata = transcript.get('metadata', {})
            candidate_name = metadata.get('candidate_name', 'Unknown')[:19]
            role_name = metadata.get('role', 'Unknown')[:24]
            round_type = metadata.get('interview_round', 'Unknown')[:14]
            
            # Extract date from created timestamp
            created = transcript.get('created', '')
            if created:
                try:
                    date_obj = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    date_str = date_obj.strftime('%Y-%m-%d')
                except:
                    date_str = 'Unknown'
            else:
                date_str = 'Unknown'
            
            size_kb = f"{transcript.get('size', 0) // 1024}KB"
            gcs_path = transcript.get('gcs_path', '')
            
            print(f"{i:<3} {date_str:<12} {candidate_name:<20} {role_name:<25} {round_type:<15} {size_kb:<8} {gcs_path}")
        
    except Exception as e:
        print(f"❌ Error searching transcripts: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Manage interview transcripts in GCP Cloud Storage",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python transcript_manager.py list
  python transcript_manager.py list --limit 20
  python transcript_manager.py download gs://your-bucket/transcripts/20241128/transcript_room123_20241128_143022.txt
  python transcript_manager.py search --candidate "John Doe"
  python transcript_manager.py search --role "Software Engineer" --date "20241128"
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available transcripts')
    list_parser.add_argument('--limit', type=int, default=100, help='Maximum number of transcripts to list')
    
    # Download command
    download_parser = subparsers.add_parser('download', help='Download a specific transcript')
    download_parser.add_argument('gcs_path', help='GCS path of the transcript to download')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search transcripts by criteria')
    search_parser.add_argument('--candidate', help='Search by candidate name')
    search_parser.add_argument('--role', help='Search by job role')
    search_parser.add_argument('--date', help='Search by date (YYYYMMDD format)')
    search_parser.add_argument('--limit', type=int, default=100, help='Maximum number of transcripts to search')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Check if required environment variables are set
    if not os.getenv('GCP_BUCKET'):
        print("❌ Error: GCP_BUCKET environment variable is not set.")
        print("Please set it in your .env file or environment.")
        sys.exit(1)
    
    if not os.path.exists('./credentials.json'):
        print("❌ Error: GCP credentials file './credentials.json' not found.")
        print("Please ensure your GCP service account credentials are available.")
        sys.exit(1)
    
    # Run the appropriate command
    try:
        if args.command == 'list':
            asyncio.run(list_transcripts(args.limit))
        elif args.command == 'download':
            asyncio.run(download_transcript(args.gcs_path))
        elif args.command == 'search':
            asyncio.run(search_transcripts(
                candidate=args.candidate,
                role=args.role,
                date=args.date,
                limit=args.limit
            ))
    except KeyboardInterrupt:
        print("\n👋 Operation cancelled by user.")
        sys.exit(0)


if __name__ == '__main__':
    main()
