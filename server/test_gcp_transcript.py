#!/usr/bin/env python3
"""
Test script for GCP Transcript Storage functionality.

This script tests the GCPTranscriptStorage class to ensure it can:
- Connect to GCP Cloud Storage
- Upload transcript files (both text and JSON formats)
- List existing transcripts
- Download transcript content

Run this script to verify your GCP setup is working correctly.
"""

import asyncio
import os
import sys
from datetime import datetime
from storage import GCPTranscriptStorage


async def test_gcp_transcript_storage():
    """Test the GCP transcript storage functionality."""
    
    print("🧪 Testing GCP Transcript Storage...")
    print("=" * 50)
    
    # Check environment variables
    print("1. Checking environment variables...")
    bucket = os.getenv('GCP_BUCKET')
    if not bucket:
        print("❌ GCP_BUCKET environment variable not set")
        return False
    print(f"✅ GCP_BUCKET: {bucket}")
    
    # Check credentials file
    print("\n2. Checking GCP credentials...")
    if not os.path.exists('./credentials.json'):
        print("❌ credentials.json file not found")
        return False
    print("✅ credentials.json file found")
    
    try:
        # Initialize storage
        print("\n3. Initializing GCP Transcript Storage...")
        storage = GCPTranscriptStorage()
        print("✅ GCP Transcript Storage initialized successfully")
        
        # Test data
        test_room_name = f"test_room_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_transcript = """[00:00] Interviewer: Hello! Welcome to the interview. Could you please introduce yourself?
[00:05] Candidate: Hi! I'm John Doe, a software engineer with 5 years of experience in Python and web development.
[00:15] Interviewer: That's great! Can you tell me about your experience with cloud technologies?
[00:20] Candidate: I've worked extensively with AWS and have some experience with Google Cloud Platform.
[00:30] Interviewer: Excellent! Let's dive deeper into your technical skills..."""
        
        test_transcript_data = {
            "transcript_entries": [
                {
                    "timestamp": 1700000000.0,
                    "elapsed_seconds": 0,
                    "speaker": "Interviewer",
                    "text": "Hello! Welcome to the interview. Could you please introduce yourself?"
                },
                {
                    "timestamp": 1700000005.0,
                    "elapsed_seconds": 5,
                    "speaker": "Candidate", 
                    "text": "Hi! I'm John Doe, a software engineer with 5 years of experience in Python and web development."
                },
                {
                    "timestamp": 1700000015.0,
                    "elapsed_seconds": 15,
                    "speaker": "Interviewer",
                    "text": "That's great! Can you tell me about your experience with cloud technologies?"
                },
                {
                    "timestamp": 1700000020.0,
                    "elapsed_seconds": 20,
                    "speaker": "Candidate",
                    "text": "I've worked extensively with AWS and have some experience with Google Cloud Platform."
                }
            ]
        }
        
        # Test text transcript upload
        print("\n4. Testing text transcript upload...")
        gcs_path_txt = await storage.save_transcript(
            transcript_content=test_transcript,
            room_name=test_room_name,
            candidate_name="John Doe",
            role="Software Engineer",
            level="Senior",
            interview_round="Technical",
            folder_prefix="test_transcripts"
        )
        print(f"✅ Text transcript uploaded: {gcs_path_txt}")
        
        # Test JSON transcript upload
        print("\n5. Testing JSON transcript upload...")
        gcs_path_json = await storage.save_transcript_json(
            transcript_data=test_transcript_data,
            room_name=test_room_name,
            candidate_name="John Doe",
            role="Software Engineer", 
            level="Senior",
            interview_round="Technical",
            folder_prefix="test_transcripts"
        )
        print(f"✅ JSON transcript uploaded: {gcs_path_json}")
        
        # Test listing transcripts
        print("\n6. Testing transcript listing...")
        transcripts = storage.list_transcripts(folder_prefix="test_transcripts", limit=10)
        print(f"✅ Found {len(transcripts)} test transcript(s)")
        
        if transcripts:
            print("\nRecent test transcripts:")
            for i, transcript in enumerate(transcripts[:3], 1):
                metadata = transcript.get('metadata', {})
                print(f"  {i}. {transcript['name']} - {metadata.get('candidate_name', 'Unknown')}")
        
        # Test downloading transcript
        print("\n7. Testing transcript download...")
        downloaded_content = await storage.download_transcript(gcs_path_txt)
        print(f"✅ Downloaded transcript ({len(downloaded_content)} characters)")
        
        # Verify content matches
        if test_transcript.strip() in downloaded_content.strip():
            print("✅ Downloaded content matches uploaded content")
        else:
            print("⚠️ Downloaded content doesn't match uploaded content")
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! GCP Transcript Storage is working correctly.")
        print("\n📋 Summary:")
        print(f"   • Text transcript: {gcs_path_txt}")
        print(f"   • JSON transcript: {gcs_path_json}")
        print(f"   • Total transcripts found: {len(transcripts)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def cleanup_test_transcripts():
    """Clean up test transcripts (optional)."""
    print("\n🧹 Cleaning up test transcripts...")
    try:
        storage = GCPTranscriptStorage()
        transcripts = storage.list_transcripts(folder_prefix="test_transcripts")
        
        if not transcripts:
            print("No test transcripts to clean up.")
            return
        
        print(f"Found {len(transcripts)} test transcript(s) to clean up.")
        cleanup = input("Do you want to delete them? (y/N): ").lower().strip()
        
        if cleanup == 'y':
            # Note: This would require additional GCS delete functionality
            # For now, just inform the user
            print("ℹ️ To delete test transcripts, use the Google Cloud Console or gsutil:")
            for transcript in transcripts:
                print(f"   gsutil rm {transcript['gcs_path']}")
        else:
            print("Test transcripts left in place.")
            
    except Exception as e:
        print(f"Error during cleanup: {e}")


def main():
    """Main function to run the tests."""
    print("🚀 GCP Transcript Storage Test Suite")
    print("This script will test your GCP Cloud Storage setup for transcript storage.\n")
    
    # Check if we're in the right directory
    if not os.path.exists('./storage'):
        print("❌ Error: This script should be run from the server directory.")
        print("Please run: cd server && python test_gcp_transcript.py")
        sys.exit(1)
    
    try:
        # Run the main test
        success = asyncio.run(test_gcp_transcript_storage())
        
        if success:
            # Ask about cleanup
            print("\n" + "-" * 50)
            cleanup = input("Run cleanup to remove test transcripts? (y/N): ").lower().strip()
            if cleanup == 'y':
                asyncio.run(cleanup_test_transcripts())
        else:
            print("\n❌ Tests failed. Please check your GCP configuration.")
            print("\nTroubleshooting tips:")
            print("1. Ensure GCP_BUCKET is set in your .env file")
            print("2. Ensure credentials.json exists and has proper permissions")
            print("3. Ensure the GCP bucket exists and is accessible")
            print("4. Check that the service account has Storage Object Admin permissions")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
